{"plugins": [{"id": 3, "description": "filename Plugin - Handles comic book management and processing", "library_path": "/root/zexuan/cpp/libs/plugins/libfilename_plugin.so", "enabled": true}, {"id": 2, "description": "unzip Plugin - <PERSON><PERSON> unzip", "library_path": "/root/zexuan/cpp/libs/plugins/libunzip_plugin.so", "enabled": true}, {"id": 1, "description": "crypto Plugin - <PERSON><PERSON> crypto", "library_path": "/root/zexuan/cpp/libs/plugins/libcrypto_plugin.so", "enabled": true}]}