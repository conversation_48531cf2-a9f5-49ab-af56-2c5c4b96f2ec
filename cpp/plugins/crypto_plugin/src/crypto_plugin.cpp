#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/plugin/plugin_export.hpp"
#include "zexuan/base/mediator.hpp"
#include "zexuan/base/message.hpp"
#include "zexuan/logger.hpp"
#include "../include/crypto_manager.hpp"
#include "../include/file_crypto_helper.hpp"
#include <filesystem>
#include <vector>
#include <iostream>
#include <mutex>
#include <fstream>

namespace fs = std::filesystem;
using namespace zexuan;

/**
 * Crypto Plugin - 加密解密插件
 * 功能：使用多种加密算法对文件进行加密和解密
 * 支持的算法：AES, RSA, SHA256, Base64等
 */
class CryptoPlugin : public zexuan::plugin::PluginBase {
private:
    std::unique_ptr<crypto::CryptoManager> cryptoManager_;
    std::mutex operationMutex_;

public:
    CryptoPlugin(std::shared_ptr<zexuan::base::Mediator> mediator, int pluginId, const std::string& description)
        : PluginBase(mediator, pluginId, description),
          cryptoManager_(std::make_unique<crypto::CryptoManager>()) {  
    }

    bool initialize() override {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        logger->info("CryptoPlugin {} initializing...", getPluginId());
        
        try {
            // 获取支持的算法列表
            auto algorithms = cryptoManager_->getSupportedAlgorithms();
            logger->info("Loaded {} crypto algorithms", algorithms.size());
            
            for (const auto& algo : algorithms) {
                logger->debug("Supported algorithm: {}", algo);
            }
            
            return true;
        } catch (const std::exception& e) {
            logger->error("Failed to initialize crypto plugin: {}", e.what());
            return false;
        }
    }

    void shutdown() override {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        logger->info("CryptoPlugin {} shutting down...", getPluginId());
        
        // 清理缓存
        if (cryptoManager_) {
            cryptoManager_->clearCache();
        }
        
        logger->info("CryptoPlugin shutdown complete");
    }

protected:
    void processMessage(const zexuan::base::Message& message) override {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        logger->debug("CryptoPlugin {} received message: TYP={}, COT={}, Source={}, Target={}",
                     getPluginId(),
                     static_cast<int>(message.getTyp()),
                     static_cast<int>(message.getCot()),
                     static_cast<int>(message.getSource()),
                     static_cast<int>(message.getTarget()));

        // 处理加密解密消息
        if (message.getFun() == 0x01 && message.getCot() == 0x06) { // 加密操作
            handleEncryptionMessage(message);
        } else if (message.getFun() == 0x02 && message.getCot() == 0x06) { // 解密操作
            handleDecryptionMessage(message);
        } else if(message.getCot() == 0x01){ // 插件信息查询
            logger->info("{}", getPluginDescription());
        }
    }

private:
    // === 核心功能方法 ===

    /**
     * @brief 处理文件加密消息
     * @param message 包含加密参数的消息
     */
    void handleEncryptionMessage(const zexuan::base::Message& message) {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");

        try {
            // 从消息中提取加密算法名称（作为可变部分传入）
            std::string algorithm = message.getTextContent();

            if (algorithm.empty()) {
                logger->error("No encryption algorithm specified in message");
                return;
            }

            logger->info("CryptoPlugin {} performing encryption with algorithm: {}", getPluginId(), algorithm);

            // 执行加密操作
            performFileEncryption(algorithm);

        } catch (const std::exception& e) {
            logger->error("Error in handleEncryptionMessage: {}", e.what());
        }
    }

    /**
     * @brief 处理文件解密消息
     * @param message 包含解密参数的消息
     */
    void handleDecryptionMessage(const zexuan::base::Message& message) {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");

        try {
            // 从消息中提取解密算法名称
            std::string algorithm = message.getTextContent();

            if (algorithm.empty()) {
                logger->error("No decryption algorithm specified in message");
                return;
            }

            logger->info("CryptoPlugin {} performing decryption with algorithm: {}", getPluginId(), algorithm);

            // 执行解密操作
            performFileDecryption(algorithm);

        } catch (const std::exception& e) {
            logger->error("Error in handleDecryptionMessage: {}", e.what());
        }
    }

    // === 核心加密解密方法 ===
    
    /**
     * @brief 执行文件加密操作
     * @param algorithm 加密算法名称
     */
    void performFileEncryption(const std::string& algorithm) {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        
        std::lock_guard<std::mutex> lock(operationMutex_);
        
        try {
            // 创建加密器实例
            auto crypto = cryptoManager_->createCrypto(algorithm);
            if (!crypto) {
                logger->error("Failed to create crypto instance for algorithm: {}", algorithm);
                return;
            }
            
            // 生成加密密钥
            std::string key = crypto->generateKey();
            logger->info("Generated encryption key for algorithm: {}", algorithm);
            
            // 定义文件路径
            std::string inputFile = "config/message";
            std::string outputFile = "config/message_crypto";
            
            // 检查输入文件是否存在
            if (!crypto::FileCryptoHelper::fileExists(inputFile)) {
                logger->error("Input file does not exist: {}", inputFile);
                return;
            }
            
            // 执行文件加密
            bool success = crypto::FileCryptoHelper::encryptFile(inputFile, outputFile, crypto, key);
            
            if (success) {
                logger->info("Successfully encrypted {} to {} using {}", inputFile, outputFile, algorithm);
                
                // 记录密钥信息（实际应用中应该安全存储）
                logger->info("Encryption key: {}", key);
                
                // 可以将密钥保存到文件或数据库中
                saveEncryptionKey(algorithm, key);
                
            } else {
                logger->error("Failed to encrypt file using algorithm: {}", algorithm);
            }
            
        } catch (const crypto::AlgorithmNotFoundException& e) {
            logger->error("Algorithm not found: {}", e.what());
        } catch (const std::exception& e) {
            logger->error("Encryption error: {}", e.what());
        }
    }
    
    /**
     * @brief 执行文件解密操作
     * @param algorithm 解密算法名称
     */
    void performFileDecryption(const std::string& algorithm) {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        
        std::lock_guard<std::mutex> lock(operationMutex_);
        
        try {
            // 创建解密器实例
            auto crypto = cryptoManager_->createCrypto(algorithm);
            if (!crypto) {
                logger->error("Failed to create crypto instance for algorithm: {}", algorithm);
                return;
            }
            
            // 加载解密密钥
            std::string key = loadEncryptionKey(algorithm);
            
            // 检查算法是否需要密钥
            if (crypto->supportsKeyGeneration() && key.empty()) {
                logger->error("No encryption key found for algorithm: {}", algorithm);
                return;
            }
            
            // 定义文件路径 - 解密到 message_uncrypto
            std::string inputFile = "config/message_crypto";
            std::string outputFile = "config/message_uncrypto";
            
            // 检查加密文件是否存在
            if (!crypto::FileCryptoHelper::fileExists(inputFile)) {
                logger->error("Encrypted file does not exist: {}", inputFile);
                return;
            }
            
            // 执行文件解密
            bool success = crypto::FileCryptoHelper::decryptFile(inputFile, outputFile, crypto, key);
            
            if (success) {
                logger->info("Successfully decrypted {} to {} using {}", inputFile, outputFile, algorithm);
            } else {
                logger->error("Failed to decrypt file using algorithm: {}", algorithm);
            }
            
        } catch (const crypto::AlgorithmNotFoundException& e) {
            logger->error("Algorithm not found: {}", e.what());
        } catch (const std::exception& e) {
            logger->error("Decryption error: {}", e.what());
        }
    }

    // === 密钥管理方法 ===
    
    /**
     * @brief 保存加密密钥
     * @param algorithm 算法名称
     * @param key 加密密钥
     */
    void saveEncryptionKey(const std::string& algorithm, const std::string& key) {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        
        try {
            // 对于不需要密钥的算法（如编码和哈希算法），跳过密钥保存
            if (key.empty()) {
                logger->info("Skipping key save for algorithm {} (no key required)", algorithm);
                return;
            }
            
            // 创建密钥文件路径
            std::string keyFile = "config/keys/" + algorithm + ".key";
            
            // 确保目录存在
            fs::create_directories(fs::path(keyFile).parent_path());
            
            // 将密钥写入文件
            std::ofstream file(keyFile);
            if (file.is_open()) {
                file << key;
                file.close();
                logger->info("Saved encryption key for algorithm: {}", algorithm);
            } else {
                logger->error("Failed to save key file: {}", keyFile);
            }
            
        } catch (const std::exception& e) {
            logger->error("Error saving encryption key: {}", e.what());
        }
    }
    
    /**
     * @brief 加载加密密钥
     * @param algorithm 算法名称
     * @return 加密密钥，如果不存在返回空字符串
     */
    std::string loadEncryptionKey(const std::string& algorithm) {
        auto logger = Logger::getFileLogger("plugin/crypto_plugin");
        
        try {
            // 检查算法是否需要密钥
            auto crypto = cryptoManager_->createCrypto(algorithm);
            if (crypto && !crypto->supportsKeyGeneration()) {
                logger->info("Algorithm {} does not require key, returning empty string", algorithm);
                return "";
            }
            
            std::string keyFile = "config/keys/" + algorithm + ".key";
            
            if (!fs::exists(keyFile)) {
                logger->warn("Key file does not exist: {}", keyFile);
                return "";
            }
            
            std::ifstream file(keyFile);
            if (file.is_open()) {
                // 读取整个文件内容（用于支持多行密钥如RSA PEM格式）
                std::string key;
                std::string line;
                bool firstLine = true;
                while (std::getline(file, line)) {
                    if (!firstLine) {
                        key += "\n";
                    }
                    key += line;
                    firstLine = false;
                }
                file.close();
                logger->info("Loaded encryption key for algorithm: {}", algorithm);
                return key;
            } else {
                logger->error("Failed to open key file: {}", keyFile);
                return "";
            }
            
        } catch (const std::exception& e) {
            logger->error("Error loading encryption key: {}", e.what());
            return "";
        }
    }
};

// === 插件导出（使用标准化宏） ===
ZEXUAN_PLUGIN_EXPORT(CryptoPlugin)
